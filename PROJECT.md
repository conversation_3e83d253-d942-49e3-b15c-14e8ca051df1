# IDC Uniform - Professional Uniforms E-commerce Platform

## 📋 Project Overview

**IDC Uniform** is a modern, responsive e-commerce platform built with Next.js 15 that specializes in professional uniforms for various industries including healthcare, hospitality, security, and industrial sectors. The platform provides a seamless shopping experience with features like quick ordering via WhatsApp, product catalogs, and comprehensive company information.

### 🎯 Project Mission
*"Professional Uniforms for Every Industry"* - Providing high-quality, industry-specific uniforms with a focus on professional appearance, comfort, and durability.

---

## 🛠️ Technology Stack

### Frontend Framework
- **Next.js 15.4.5** - React-based full-stack framework with App Router
- **React 19.1.0** - Component-based UI library
- **TypeScript 5** - Type-safe JavaScript development

### Styling & UI
- **Tailwind CSS 4** - Utility-first CSS framework
- **Radix UI** - Headless UI components for accessibility
- **Framer Motion 12.23.12** - Animation library
- **Lucide React** - Icon library
- **Class Variance Authority** - Component variant styling
- **next-themes** - Dark/light theme management

### State Management
- **Zustand 5.0.7** - Lightweight state management

### Development Tools
- **ESLint 9** - Code linting and quality
- **PostCSS** - CSS processing
- **Turbopack** - Fast development bundler

---

## 📁 Project Structure

```
idc-uniform/
├── 📄 Configuration Files
│   ├── package.json              # Dependencies and scripts
│   ├── next.config.ts            # Next.js configuration
│   ├── tsconfig.json             # TypeScript configuration
│   ├── eslint.config.mjs         # ESLint configuration
│   ├── postcss.config.mjs        # PostCSS configuration
│   └── README.md                 # Basic project documentation
│
├── 📁 public/                    # Static assets
│   ├── file.svg
│   ├── globe.svg
│   ├── next.svg
│   ├── vercel.svg
│   └── window.svg
│
└── 📁 src/                       # Source code
    ├── 📁 app/                   # Next.js App Router pages
    │   ├── layout.tsx            # Root layout component
    │   ├── page.tsx              # Homepage
    │   ├── loading.tsx           # Loading UI
    │   ├── globals.css           # Global styles
    │   ├── favicon.ico           # Site favicon
    │   ├── robots.ts             # SEO robots configuration
    │   ├── sitemap.ts            # SEO sitemap generation
    │   ├── about/                # About page
    │   ├── contact/              # Contact page
    │   ├── products/             # Products catalog
    │   │   └── [id]/             # Dynamic product detail pages
    │   ├── services/             # Services page
    │   └── size-guide/           # Size guide page
    │
    ├── 📁 components/            # Reusable UI components
    │   ├── category-card.tsx     # Product category display
    │   ├── footer.tsx            # Site footer
    │   ├── navigation.tsx        # Site navigation
    │   ├── placeholder-image.tsx # Image placeholder component
    │   ├── product-card.tsx      # Product display card
    │   ├── quick-order-dialog.tsx # WhatsApp quick order modal
    │   ├── theme-provider.tsx    # Theme context provider
    │   ├── theme-toggle.tsx      # Dark/light mode toggle
    │   ├── whatsapp-contact.tsx  # WhatsApp contact component
    │   └── ui/                   # Base UI components
    │       ├── badge.tsx         # Badge component
    │       ├── button.tsx        # Button component
    │       ├── card.tsx          # Card component
    │       ├── dialog.tsx        # Modal dialog component
    │       ├── input.tsx         # Input field component
    │       ├── label.tsx         # Label component
    │       ├── select.tsx        # Select dropdown component
    │       └── tabs.tsx          # Tabs component
    │
    ├── 📁 data/                  # Static data files
    │   ├── categories.json       # Product categories data
    │   ├── company.json          # Company information
    │   └── products.json         # Product catalog data
    │
    ├── 📁 lib/                   # Utility functions
    │   └── utils.ts              # Common utility functions
    │
    └── 📁 types/                 # TypeScript type definitions
        └── index.ts              # Global type definitions
```

---

## 🎨 Key Features

### 🏪 E-commerce Functionality
- **Product Catalog** - Organized by industry categories
- **Dynamic Product Pages** - Individual product detail views
- **Category Navigation** - Easy browsing by uniform type
- **Quick Order System** - WhatsApp integration for fast ordering

### 🎯 Industry Focus
- **Hospital Uniforms** - Scrubs, lab coats, nurse uniforms
- **Hotel Uniforms** - Front desk, housekeeping, restaurant staff
- **Security Uniforms** - Professional security and guard attire
- **Industrial Uniforms** - Safety and workwear solutions
- **Custom Solutions** - Tailored uniform services

### 🌟 User Experience
- **Responsive Design** - Mobile-first approach
- **Dark/Light Theme** - User preference theme switching
- **Accessibility** - WCAG compliant with Radix UI components
- **Performance** - Optimized with Next.js 15 and Turbopack
- **SEO Optimized** - Structured data and meta tags

### 📱 Contact & Communication
- **WhatsApp Integration** - Direct contact for orders and inquiries
- **Multiple Contact Methods** - Phone, email, social media
- **Location Information** - Physical store and warehouse details

---

## 🚀 Development Scripts

```bash
# Development
npm run dev          # Start development server with Turbopack

# Production
npm run build        # Build for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint for code quality
```

---

## 📊 Data Structure

### Categories
- Industry-specific uniform categories
- Subcategory organization
- Category descriptions and imagery

### Products
- Detailed product information
- Pricing and sizing options
- Image galleries and specifications

### Company Information
- Business details and contact information
- Multiple location support
- Social media integration

---

## 🔧 Development Setup

1. **Prerequisites**
   - Node.js 18+ 
   - npm/yarn/pnpm package manager

2. **Installation**
   ```bash
   npm install
   ```

3. **Development**
   ```bash
   npm run dev
   ```

4. **Environment**
   - Runs on `http://localhost:3000`
   - Hot reload enabled
   - TypeScript type checking

---

## 📈 Project Goals

### Business Objectives
- Streamline uniform ordering process
- Showcase industry-specific solutions
- Provide professional online presence
- Enable direct customer communication

### Technical Objectives
- Modern, maintainable codebase
- Excellent performance and SEO
- Responsive, accessible design
- Scalable architecture

---

## 🤝 Contact Information

**Company**: IDC Uniform  
**Tagline**: Professional Uniforms for Every Industry  
**WhatsApp**: +919895057063  
**Email**: <EMAIL>  

**Social Media**:
- Facebook: [facebook.com/idcuniform](https://facebook.com/idcuniform)
- Instagram: [instagram.com/idcuniform](https://instagram.com/idcuniform)
- LinkedIn: [linkedin.com/company/idcuniform](https://linkedin.com/company/idcuniform)

---

## 📝 Notes

This project represents a modern approach to e-commerce in the uniform industry, combining the latest web technologies with user-centered design principles to create an effective business platform.

*Last updated: August 4, 2025*
