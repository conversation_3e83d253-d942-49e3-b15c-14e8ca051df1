@import "tailwindcss";

:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #000000;
  --accent: #f5f5f5;
  --accent-foreground: #000000;
  --muted: #f5f5f5;
  --muted-foreground: #6b7280;
  --border: #e5e5e5;
  --card: #ffffff;
  --card-foreground: #000000;
  --primary: #000000;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #000000;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #000000;
}

[data-theme="dark"] {
  /* Dark theme colors */
  --background: #000000;
  --foreground: #ffffff;
  --accent: #1a1a1a;
  --accent-foreground: #ffffff;
  --muted: #1a1a1a;
  --muted-foreground: #9ca3af;
  --border: #333333;
  --card: #000000;
  --card-foreground: #ffffff;
  --primary: #ffffff;
  --primary-foreground: #000000;
  --secondary: #1a1a1a;
  --secondary-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-ring: var(--ring);
  --font-sans: Inter, system-ui, sans-serif;
  --font-mono: "JetBrains Mono", monospace;
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --background: #000000;
    --foreground: #ffffff;
    --accent: #1a1a1a;
    --accent-foreground: #ffffff;
    --muted: #1a1a1a;
    --muted-foreground: #9ca3af;
    --border: #333333;
    --card: #000000;
    --card-foreground: #ffffff;
    --primary: #ffffff;
    --primary-foreground: #000000;
    --secondary: #1a1a1a;
    --secondary-foreground: #ffffff;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --ring: #ffffff;
  }
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
