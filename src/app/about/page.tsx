import { Navigation } from "@/components/navigation"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, Award, Clock, Globe, Target, Heart } from "lucide-react"

export default function AboutPage() {
  const stats = [
    { label: "Years of Experience", value: "15+", icon: Clock },
    { label: "Happy Customers", value: "5000+", icon: Users },
    { label: "Industries Served", value: "50+", icon: Globe },
    { label: "Quality Awards", value: "25+", icon: Award },
  ]

  const values = [
    {
      icon: Target,
      title: "Quality First",
      description: "We never compromise on quality. Every uniform is crafted with premium materials and attention to detail."
    },
    {
      icon: Users,
      title: "Customer Focus",
      description: "Our customers are at the heart of everything we do. We listen, understand, and deliver solutions that exceed expectations."
    },
    {
      icon: Heart,
      title: "Integrity",
      description: "We conduct business with honesty, transparency, and ethical practices in all our relationships."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "We strive for excellence in every aspect of our business, from product design to customer service."
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold mb-4">About IDC Uniform</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            For over 15 years, IDC Uniform has been the trusted partner for businesses seeking 
            high-quality, professional uniforms that enhance their brand image and employee confidence.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center">
              <CardContent className="pt-6">
                <stat.icon className="h-8 w-8 mx-auto mb-4 text-primary" />
                <div className="text-3xl font-bold mb-2">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Our Story */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold mb-6">Our Story</h2>
            <div className="space-y-4 text-muted-foreground">
              <p>
                Founded in 2009, IDC Uniform began as a small family business with a simple mission: 
                to provide high-quality, professional uniforms that help businesses present their best image.
              </p>
              <p>
                What started as a local uniform supplier has grown into a trusted partner for businesses 
                across multiple industries, from healthcare and hospitality to security and industrial sectors.
              </p>
              <p>
                Our commitment to quality, customer service, and innovation has earned us the trust of 
                over 5,000 satisfied customers and numerous industry awards.
              </p>
              <p>
                Today, we continue to evolve and expand our offerings while maintaining the personal 
                touch and attention to detail that has been our hallmark since day one.
              </p>
            </div>
          </div>
          
          <div className="bg-muted rounded-lg p-8 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="text-8xl mb-4">🏢</div>
              <div className="text-lg font-medium">IDC Uniform Headquarters</div>
            </div>
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-6 w-6 text-primary" />
                Our Mission
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                To empower businesses with professional uniform solutions that enhance their brand image, 
                boost employee confidence, and contribute to their success. We are committed to delivering 
                exceptional quality, service, and value in everything we do.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-6 w-6 text-primary" />
                Our Vision
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                To be the leading uniform provider recognized for innovation, quality, and customer satisfaction. 
                We envision a future where every professional feels confident and proud in their uniform, 
                knowing it represents the highest standards of their industry.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Values</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <value.icon className="h-12 w-12 mx-auto mb-4 text-primary" />
                  <CardTitle className="text-lg">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{value.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Industries We Serve */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-8">Industries We Serve</h2>
          <div className="flex flex-wrap justify-center gap-3">
            {[
              "Healthcare", "Hospitality", "Security", "Industrial", "Corporate", 
              "Education", "Food Service", "Retail", "Transportation", "Government",
              "Manufacturing", "Construction", "Cleaning Services", "Event Management"
            ].map((industry) => (
              <Badge key={industry} variant="secondary" className="px-4 py-2">
                {industry}
              </Badge>
            ))}
          </div>
        </div>

        {/* Why Choose Us */}
        <Card className="bg-primary text-primary-foreground">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Why Choose IDC Uniform?</CardTitle>
            <CardDescription className="text-primary-foreground/80">
              Experience the difference that quality and service make
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <Award className="h-8 w-8 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Premium Quality</h3>
                <p className="text-sm text-primary-foreground/80">
                  Only the finest materials and craftsmanship
                </p>
              </div>
              <div>
                <Users className="h-8 w-8 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Expert Service</h3>
                <p className="text-sm text-primary-foreground/80">
                  Dedicated support from consultation to delivery
                </p>
              </div>
              <div>
                <Clock className="h-8 w-8 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Fast Turnaround</h3>
                <p className="text-sm text-primary-foreground/80">
                  Quick delivery without compromising quality
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  )
}
