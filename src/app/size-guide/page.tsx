import { Footer } from "@/components/footer"
import { Navigation } from "@/components/navigation"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ArrowLeft, Info, Ruler, Users } from "lucide-react"
import Link from "next/link"

export default function SizeGuidePage() {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 mb-8">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/products">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Products
            </Link>
          </Button>
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Ruler className="w-8 h-8 text-primary" />
            <h1 className="text-4xl lg:text-5xl font-bold">Size Guide</h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Find your perfect fit with our comprehensive sizing charts for all uniform categories
          </p>
        </div>

        {/* How to Measure Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              How to Measure
            </CardTitle>
            <CardDescription>
              Follow these simple steps to get accurate measurements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-3">For Best Results:</h3>
                <ul className="space-y-2 text-sm">
                  <li>• Use a flexible measuring tape</li>
                  <li>• Measure over undergarments or close-fitting clothes</li>
                  <li>• Keep the tape snug but not tight</li>
                  <li>• Stand straight with arms at your sides</li>
                  <li>• Have someone help you for accurate measurements</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-3">Key Measurements:</h3>
                <ul className="space-y-2 text-sm">
                  <li>• <strong>Chest/Bust:</strong> Around the fullest part</li>
                  <li>• <strong>Waist:</strong> Around the narrowest part</li>
                  <li>• <strong>Hips:</strong> Around the fullest part</li>
                  <li>• <strong>Inseam:</strong> From crotch to ankle</li>
                  <li>• <strong>Sleeve:</strong> From shoulder to wrist</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Size Charts by Category */}
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="medical">Medical</TabsTrigger>
            <TabsTrigger value="hospitality">Hospitality</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="industrial">Industrial</TabsTrigger>
          </TabsList>

          {/* General Sizing */}
          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>General Uniform Sizing</CardTitle>
                <CardDescription>Standard sizing for most uniform categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid lg:grid-cols-2 gap-6">
                  {/* Men's Sizing */}
                  <div>
                    <h3 className="font-semibold mb-4 flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      Men's Sizing
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">Size</th>
                            <th className="text-left p-2">Chest (in)</th>
                            <th className="text-left p-2">Waist (in)</th>
                            <th className="text-left p-2">Sleeve (in)</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">XS</Badge></td>
                            <td className="p-2">32-34</td>
                            <td className="p-2">28-30</td>
                            <td className="p-2">32-33</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">S</Badge></td>
                            <td className="p-2">34-36</td>
                            <td className="p-2">30-32</td>
                            <td className="p-2">33-34</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">M</Badge></td>
                            <td className="p-2">38-40</td>
                            <td className="p-2">32-34</td>
                            <td className="p-2">34-35</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">L</Badge></td>
                            <td className="p-2">42-44</td>
                            <td className="p-2">36-38</td>
                            <td className="p-2">35-36</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">XL</Badge></td>
                            <td className="p-2">46-48</td>
                            <td className="p-2">40-42</td>
                            <td className="p-2">36-37</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                            <td className="p-2">50-52</td>
                            <td className="p-2">44-46</td>
                            <td className="p-2">37-38</td>
                          </tr>
                          <tr>
                            <td className="p-2"><Badge variant="outline">XXXL</Badge></td>
                            <td className="p-2">54-56</td>
                            <td className="p-2">48-50</td>
                            <td className="p-2">38-39</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Women's Sizing */}
                  <div>
                    <h3 className="font-semibold mb-4 flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      Women's Sizing
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">Size</th>
                            <th className="text-left p-2">Bust (in)</th>
                            <th className="text-left p-2">Waist (in)</th>
                            <th className="text-left p-2">Hips (in)</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">XS</Badge></td>
                            <td className="p-2">30-32</td>
                            <td className="p-2">24-26</td>
                            <td className="p-2">34-36</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">S</Badge></td>
                            <td className="p-2">32-34</td>
                            <td className="p-2">26-28</td>
                            <td className="p-2">36-38</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">M</Badge></td>
                            <td className="p-2">36-38</td>
                            <td className="p-2">30-32</td>
                            <td className="p-2">40-42</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">L</Badge></td>
                            <td className="p-2">40-42</td>
                            <td className="p-2">34-36</td>
                            <td className="p-2">44-46</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">XL</Badge></td>
                            <td className="p-2">44-46</td>
                            <td className="p-2">38-40</td>
                            <td className="p-2">48-50</td>
                          </tr>
                          <tr className="border-b">
                            <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                            <td className="p-2">48-50</td>
                            <td className="p-2">42-44</td>
                            <td className="p-2">52-54</td>
                          </tr>
                          <tr>
                            <td className="p-2"><Badge variant="outline">XXXL</Badge></td>
                            <td className="p-2">52-54</td>
                            <td className="p-2">46-48</td>
                            <td className="p-2">56-58</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Medical Sizing */}
          <TabsContent value="medical" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Medical Scrubs & Lab Coats</CardTitle>
                <CardDescription>Specialized sizing for healthcare professionals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Medical Fit Notes:</h4>
                    <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                      <li>• Scrubs are designed for comfort during long shifts</li>
                      <li>• Consider ordering one size up for layering</li>
                      <li>• Lab coats typically run longer for professional appearance</li>
                    </ul>
                  </div>
                  
                  <div className="grid lg:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-semibold mb-4">Scrub Tops & Bottoms</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Size</th>
                              <th className="text-left p-2">Chest/Bust</th>
                              <th className="text-left p-2">Length</th>
                              <th className="text-left p-2">Inseam</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XS</Badge></td>
                              <td className="p-2">32-34</td>
                              <td className="p-2">25-26</td>
                              <td className="p-2">29-30</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">S</Badge></td>
                              <td className="p-2">34-36</td>
                              <td className="p-2">26-27</td>
                              <td className="p-2">30-31</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">M</Badge></td>
                              <td className="p-2">38-40</td>
                              <td className="p-2">27-28</td>
                              <td className="p-2">31-32</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">L</Badge></td>
                              <td className="p-2">42-44</td>
                              <td className="p-2">28-29</td>
                              <td className="p-2">32-33</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XL</Badge></td>
                              <td className="p-2">46-48</td>
                              <td className="p-2">29-30</td>
                              <td className="p-2">33-34</td>
                            </tr>
                            <tr>
                              <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                              <td className="p-2">50-52</td>
                              <td className="p-2">30-31</td>
                              <td className="p-2">34-35</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-semibold mb-4">Lab Coats</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Size</th>
                              <th className="text-left p-2">Chest</th>
                              <th className="text-left p-2">Length</th>
                              <th className="text-left p-2">Sleeve</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XS</Badge></td>
                              <td className="p-2">34-36</td>
                              <td className="p-2">37-38</td>
                              <td className="p-2">23-24</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">S</Badge></td>
                              <td className="p-2">36-38</td>
                              <td className="p-2">38-39</td>
                              <td className="p-2">24-25</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">M</Badge></td>
                              <td className="p-2">40-42</td>
                              <td className="p-2">39-40</td>
                              <td className="p-2">25-26</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">L</Badge></td>
                              <td className="p-2">44-46</td>
                              <td className="p-2">40-41</td>
                              <td className="p-2">26-27</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XL</Badge></td>
                              <td className="p-2">48-50</td>
                              <td className="p-2">41-42</td>
                              <td className="p-2">27-28</td>
                            </tr>
                            <tr>
                              <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                              <td className="p-2">52-54</td>
                              <td className="p-2">42-43</td>
                              <td className="p-2">28-29</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Hospitality Sizing */}
          <TabsContent value="hospitality" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Hotel & Restaurant Uniforms</CardTitle>
                <CardDescription>Professional sizing for hospitality industry</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-amber-50 dark:bg-amber-950 p-4 rounded-lg">
                    <h4 className="font-semibold text-amber-900 dark:text-amber-100 mb-2">Hospitality Fit Notes:</h4>
                    <ul className="text-sm text-amber-800 dark:text-amber-200 space-y-1">
                      <li>• Professional appearance is key in hospitality</li>
                      <li>• Consider movement and comfort for long shifts</li>
                      <li>• Front-of-house uniforms may run more fitted</li>
                    </ul>
                  </div>

                  <div className="grid lg:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-semibold mb-4">Dress Shirts & Blouses</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Size</th>
                              <th className="text-left p-2">Chest/Bust</th>
                              <th className="text-left p-2">Neck</th>
                              <th className="text-left p-2">Sleeve</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XS</Badge></td>
                              <td className="p-2">32-34</td>
                              <td className="p-2">14-14.5</td>
                              <td className="p-2">32-33</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">S</Badge></td>
                              <td className="p-2">34-36</td>
                              <td className="p-2">15-15.5</td>
                              <td className="p-2">33-34</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">M</Badge></td>
                              <td className="p-2">38-40</td>
                              <td className="p-2">16-16.5</td>
                              <td className="p-2">34-35</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">L</Badge></td>
                              <td className="p-2">42-44</td>
                              <td className="p-2">17-17.5</td>
                              <td className="p-2">35-36</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XL</Badge></td>
                              <td className="p-2">46-48</td>
                              <td className="p-2">18-18.5</td>
                              <td className="p-2">36-37</td>
                            </tr>
                            <tr>
                              <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                              <td className="p-2">50-52</td>
                              <td className="p-2">19-19.5</td>
                              <td className="p-2">37-38</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-semibold mb-4">Aprons & Vests</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Size</th>
                              <th className="text-left p-2">Chest</th>
                              <th className="text-left p-2">Length</th>
                              <th className="text-left p-2">Width</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XS</Badge></td>
                              <td className="p-2">32-34</td>
                              <td className="p-2">28-30</td>
                              <td className="p-2">24-26</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">S</Badge></td>
                              <td className="p-2">34-36</td>
                              <td className="p-2">30-32</td>
                              <td className="p-2">26-28</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">M</Badge></td>
                              <td className="p-2">38-40</td>
                              <td className="p-2">32-34</td>
                              <td className="p-2">28-30</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">L</Badge></td>
                              <td className="p-2">42-44</td>
                              <td className="p-2">34-36</td>
                              <td className="p-2">30-32</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XL</Badge></td>
                              <td className="p-2">46-48</td>
                              <td className="p-2">36-38</td>
                              <td className="p-2">32-34</td>
                            </tr>
                            <tr>
                              <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                              <td className="p-2">50-52</td>
                              <td className="p-2">38-40</td>
                              <td className="p-2">34-36</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Sizing */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Security & Guard Uniforms</CardTitle>
                <CardDescription>Durable sizing for security professionals</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-slate-50 dark:bg-slate-950 p-4 rounded-lg">
                    <h4 className="font-semibold text-slate-900 dark:text-slate-100 mb-2">Security Fit Notes:</h4>
                    <ul className="text-sm text-slate-800 dark:text-slate-200 space-y-1">
                      <li>• Uniforms designed for durability and movement</li>
                      <li>• Consider room for equipment and accessories</li>
                      <li>• Professional appearance maintains authority</li>
                    </ul>
                  </div>

                  <div className="grid lg:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-semibold mb-4">Security Shirts</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Size</th>
                              <th className="text-left p-2">Chest</th>
                              <th className="text-left p-2">Length</th>
                              <th className="text-left p-2">Sleeve</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">S</Badge></td>
                              <td className="p-2">36-38</td>
                              <td className="p-2">29-30</td>
                              <td className="p-2">33-34</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">M</Badge></td>
                              <td className="p-2">40-42</td>
                              <td className="p-2">30-31</td>
                              <td className="p-2">34-35</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">L</Badge></td>
                              <td className="p-2">44-46</td>
                              <td className="p-2">31-32</td>
                              <td className="p-2">35-36</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XL</Badge></td>
                              <td className="p-2">48-50</td>
                              <td className="p-2">32-33</td>
                              <td className="p-2">36-37</td>
                            </tr>
                            <tr>
                              <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                              <td className="p-2">52-54</td>
                              <td className="p-2">33-34</td>
                              <td className="p-2">37-38</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-semibold mb-4">Security Pants</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Size</th>
                              <th className="text-left p-2">Waist</th>
                              <th className="text-left p-2">Inseam</th>
                              <th className="text-left p-2">Outseam</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">30</Badge></td>
                              <td className="p-2">30</td>
                              <td className="p-2">30-34</td>
                              <td className="p-2">42-46</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">32</Badge></td>
                              <td className="p-2">32</td>
                              <td className="p-2">30-34</td>
                              <td className="p-2">42-46</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">34</Badge></td>
                              <td className="p-2">34</td>
                              <td className="p-2">30-34</td>
                              <td className="p-2">42-46</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">36</Badge></td>
                              <td className="p-2">36</td>
                              <td className="p-2">30-34</td>
                              <td className="p-2">42-46</td>
                            </tr>
                            <tr>
                              <td className="p-2"><Badge variant="outline">38</Badge></td>
                              <td className="p-2">38</td>
                              <td className="p-2">30-34</td>
                              <td className="p-2">42-46</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Industrial Sizing */}
          <TabsContent value="industrial" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Industrial & Safety Workwear</CardTitle>
                <CardDescription>Heavy-duty sizing for industrial environments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-orange-50 dark:bg-orange-950 p-4 rounded-lg">
                    <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-2">Industrial Fit Notes:</h4>
                    <ul className="text-sm text-orange-800 dark:text-orange-200 space-y-1">
                      <li>• Built for durability in harsh environments</li>
                      <li>• Loose fit allows for layering and movement</li>
                      <li>• Safety features may affect sizing</li>
                    </ul>
                  </div>

                  <div className="grid lg:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-semibold mb-4">Coveralls & Overalls</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Size</th>
                              <th className="text-left p-2">Chest</th>
                              <th className="text-left p-2">Waist</th>
                              <th className="text-left p-2">Inseam</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">S</Badge></td>
                              <td className="p-2">36-38</td>
                              <td className="p-2">30-32</td>
                              <td className="p-2">30-31</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">M</Badge></td>
                              <td className="p-2">40-42</td>
                              <td className="p-2">32-34</td>
                              <td className="p-2">31-32</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">L</Badge></td>
                              <td className="p-2">44-46</td>
                              <td className="p-2">36-38</td>
                              <td className="p-2">32-33</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XL</Badge></td>
                              <td className="p-2">48-50</td>
                              <td className="p-2">40-42</td>
                              <td className="p-2">33-34</td>
                            </tr>
                            <tr>
                              <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                              <td className="p-2">52-54</td>
                              <td className="p-2">44-46</td>
                              <td className="p-2">34-35</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-semibold mb-4">Safety Vests & Hi-Vis</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Size</th>
                              <th className="text-left p-2">Chest</th>
                              <th className="text-left p-2">Length</th>
                              <th className="text-left p-2">Shoulder</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">S</Badge></td>
                              <td className="p-2">36-38</td>
                              <td className="p-2">26-27</td>
                              <td className="p-2">17-18</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">M</Badge></td>
                              <td className="p-2">40-42</td>
                              <td className="p-2">27-28</td>
                              <td className="p-2">18-19</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">L</Badge></td>
                              <td className="p-2">44-46</td>
                              <td className="p-2">28-29</td>
                              <td className="p-2">19-20</td>
                            </tr>
                            <tr className="border-b">
                              <td className="p-2"><Badge variant="outline">XL</Badge></td>
                              <td className="p-2">48-50</td>
                              <td className="p-2">29-30</td>
                              <td className="p-2">20-21</td>
                            </tr>
                            <tr>
                              <td className="p-2"><Badge variant="outline">XXL</Badge></td>
                              <td className="p-2">52-54</td>
                              <td className="p-2">30-31</td>
                              <td className="p-2">21-22</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Contact Section */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle className="text-center">Need Help with Sizing?</CardTitle>
            <CardDescription className="text-center">
              Our sizing experts are here to help you find the perfect fit
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground mb-6">
              If you're unsure about sizing or need custom measurements, don't hesitate to contact us.
              We offer free consultations and can provide personalized sizing recommendations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild>
                <Link href="/contact">Contact Our Sizing Experts</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/products">Browse Products</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  )
}
