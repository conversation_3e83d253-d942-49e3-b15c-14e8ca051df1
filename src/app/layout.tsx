import { ThemeProvider } from "@/components/theme-provider";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "IDC Uniform - Professional Uniforms for Every Industry",
  description: "IDC Uniform specializes in high-quality uniforms for hospitals, hotels, security, industrial, and custom applications. Professional attire solutions for your business needs.",
  keywords: ["uniforms", "medical scrubs", "hotel uniforms", "security uniforms", "industrial workwear", "custom uniforms"],
  authors: [{ name: "IDC Uniform" }],
  creator: "IDC Uniform",
  publisher: "IDC Uniform",
  openGraph: {
    title: "IDC Uniform - Professional Uniforms for Every Industry",
    description: "Professional uniforms for hospitals, hotels, security, industrial, and custom applications.",
    url: "https://idcuniform.com",
    siteName: "IDC Uniform",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "IDC Uniform - Professional Uniforms for Every Industry",
    description: "Professional uniforms for hospitals, hotels, security, industrial, and custom applications.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
