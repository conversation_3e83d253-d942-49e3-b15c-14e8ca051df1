import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: string | number): string {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(numPrice)
}

export function generateWhatsAppMessage(product: { name: string; id: string }, selections: { size: string; quantity: number; color?: string; customerName: string; customerPhone: string; companyName?: string; notes?: string }): string {
  return `Hello IDC Uniform!

I'm interested in:
Product: ${product.name}
Product Code: ${product.id}
Size: ${selections.size}
Quantity: ${selections.quantity}
Color: ${selections.color || 'Standard'}

Customer Details:
Name: ${selections.customerName}
Phone: ${selections.customerPhone}
Company: ${selections.companyName || 'Individual'}

Additional Notes:
${selections.notes || 'None'}

Please provide pricing and availability.`
}

export function sanitizeForWhatsApp(message: string): string {
  // Clean up the message for WhatsApp compatibility
  return message
    .replace(/\n\n/g, '\n') // Reduce multiple line breaks
    .trim()
}

export function generateWhatsAppUrl(message: string, phoneNumber: string): string {
  // Clean phone number (remove any non-digits except +)
  const cleanPhone = phoneNumber.replace(/[^\d+]/g, '')
  
  // For WhatsApp URLs, we need to be more careful with encoding
  // Use encodeURIComponent but preserve line breaks as %0A
  const encodedMessage = encodeURIComponent(message)
  
  return `https://wa.me/${cleanPhone}?text=${encodedMessage}`
}
