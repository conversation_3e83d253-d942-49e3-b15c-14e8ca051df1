import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Category } from "@/types"
import { ArrowRight } from "lucide-react"
import Link from "next/link"

interface CategoryCardProps {
  category: Category
}

export function CategoryCard({ category }: CategoryCardProps) {
  return (
    <Card className="group overflow-hidden transition-all duration-300 hover:shadow-lg">
      <CardHeader className="p-0">
        <div className="relative aspect-[4/3] overflow-hidden bg-muted flex items-center justify-center">
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          <div className="text-center text-muted-foreground">
            <div className="text-4xl mb-2">👔</div>
            <div className="text-sm font-medium">{category.name}</div>
          </div>
          <div className="absolute bottom-4 left-4 text-white">
            <h3 className="text-xl font-bold mb-1">{category.name}</h3>
            <p className="text-sm opacity-90">{category.description}</p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex flex-wrap gap-1">
            {category.subcategories.slice(0, 3).map((subcategory) => (
              <span
                key={subcategory}
                className="text-xs bg-muted px-2 py-1 rounded-md text-muted-foreground"
              >
                {subcategory}
              </span>
            ))}
            {category.subcategories.length > 3 && (
              <span className="text-xs bg-muted px-2 py-1 rounded-md text-muted-foreground">
                +{category.subcategories.length - 3} more
              </span>
            )}
          </div>
          
          <Button asChild className="w-full group">
            <Link href={`/products?category=${category.id}`}>
              Browse {category.name}
              <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
