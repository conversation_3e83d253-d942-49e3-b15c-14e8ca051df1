"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Product, ProductSelection } from "@/types"
import { generateWhatsAppMessage, generateWhatsAppUrl } from "@/lib/utils"
import { MessageCircle, ShoppingCart } from "lucide-react"

interface QuickOrderDialogProps {
  product: Product
  companyWhatsApp: string
  children: React.ReactNode
}

export function QuickOrderDialog({ product, companyWhatsApp, children }: QuickOrderDialogProps) {
  const [open, setOpen] = useState(false)
  const [selection, setSelection] = useState<Partial<ProductSelection>>({
    size: "",
    color: product.colors[0] || "",
    quantity: 1,
    customerName: "",
    customerPhone: "",
    companyName: "",
    notes: "",
  })

  const handleInputChange = (field: keyof ProductSelection, value: string | number) => {
    setSelection(prev => ({ ...prev, [field]: value }))
  }

  const handleWhatsAppContact = () => {
    if (!selection.size || !selection.customerName || !selection.customerPhone) {
      alert("Please fill in all required fields")
      return
    }

    const message = generateWhatsAppMessage(product, selection as ProductSelection)
    const whatsappUrl = generateWhatsAppUrl(message, companyWhatsApp)
    window.open(whatsappUrl, "_blank")
    setOpen(false)
    
    // Reset form
    setSelection({
      size: "",
      color: product.colors[0] || "",
      quantity: 1,
      customerName: "",
      customerPhone: "",
      companyName: "",
      notes: "",
    })
  }

  const isFormValid = selection.size && selection.customerName && selection.customerPhone

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Quick Order - {product.name}
          </DialogTitle>
          <DialogDescription>
            Fill in your details to get a quick quote via WhatsApp
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="size">Size *</Label>
              <Select onValueChange={(value) => handleInputChange("size", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  {product.sizes.map((size) => (
                    <SelectItem key={size} value={size}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Select 
                value={selection.color} 
                onValueChange={(value) => handleInputChange("color", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select color" />
                </SelectTrigger>
                <SelectContent>
                  {product.colors.map((color) => (
                    <SelectItem key={color} value={color}>
                      {color}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              value={selection.quantity}
              onChange={(e) => handleInputChange("quantity", parseInt(e.target.value) || 1)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="customerName">Your Name *</Label>
            <Input
              id="customerName"
              value={selection.customerName}
              onChange={(e) => handleInputChange("customerName", e.target.value)}
              placeholder="Enter your full name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="customerPhone">Phone Number *</Label>
            <Input
              id="customerPhone"
              value={selection.customerPhone}
              onChange={(e) => handleInputChange("customerPhone", e.target.value)}
              placeholder="Enter your phone number"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyName">Company Name (Optional)</Label>
            <Input
              id="companyName"
              value={selection.companyName}
              onChange={(e) => handleInputChange("companyName", e.target.value)}
              placeholder="Enter your company name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <Input
              id="notes"
              value={selection.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Any special requirements or questions"
            />
          </div>

          <Button 
            onClick={handleWhatsAppContact}
            disabled={!isFormValid}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Send WhatsApp Message
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
