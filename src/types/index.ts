export interface Company {
  name: string
  tagline: string
  whatsapp: string
  email: string
  socialMedia: {
    facebook?: string
    instagram?: string
    linkedin?: string
  }
}

export interface Location {
  id: string
  name: string
  address: string
  phone: string
  hours: string
  mapUrl?: string
}

export interface Category {
  id: string
  name: string
  description: string
  image: string
  subcategories: string[]
}

export interface Product {
  id: string
  name: string
  category: string
  price: string
  sizes: string[]
  colors: string[]
  images: string[]
  description: string
  features: string[]
  inStock: boolean
}

export interface CartItem {
  product: Product
  size: string
  color: string
  quantity: number
}

export interface CustomerDetails {
  customerName: string
  customerPhone: string
  companyName?: string
  notes?: string
}

export interface ProductSelection extends CustomerDetails {
  size: string
  color: string
  quantity: number
}

export type Size = 'XS' | 'S' | 'M' | 'L' | 'XL' | 'XXL' | 'XXXL'
